import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class CameraControlsWidget extends StatelessWidget {
  final VoidCallback onCapturePhoto;
  final VoidCallback onOpenGallery;
  final VoidCallback onToggleFlash;
  final FlashMode currentFlashMode;
  final bool isProcessing;

  const CameraControlsWidget({
    super.key,
    required this.onCapturePhoto,
    required this.onOpenGallery,
    required this.onToggleFlash,
    required this.currentFlashMode,
    required this.isProcessing,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: <PERSON><PERSON><PERSON>(
        top: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Gallery button
            GestureDetector(
              onTap: isProcessing ? null : () {
                debugPrint('Gallery button tapped');
                onOpenGallery();
              },
              child: Container(
                width: 14.w,
                height: 14.w,
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.colorScheme.surface
                      .withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.lightTheme.colorScheme.surface
                        .withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                ),
                child: Center(
                  child: CustomIconWidget(
                    iconName: 'photo_library',
                    color: isProcessing
                        ? AppTheme.lightTheme.colorScheme.surface
                            .withValues(alpha: 0.5)
                        : AppTheme.lightTheme.colorScheme.surface,
                    size: 24,
                  ),
                ),
              ),
            ),

            // Capture button
            GestureDetector(
              onTap: isProcessing ? null : () {
                debugPrint('Capture button tapped');
                onCapturePhoto();
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  color: isProcessing
                      ? AppTheme.lightTheme.colorScheme.surface
                          .withValues(alpha: 0.5)
                      : AppTheme.lightTheme.colorScheme.primary,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.lightTheme.colorScheme.surface,
                    width: 4.0,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8.0,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Center(
                  child: isProcessing
                      ? SizedBox(
                          width: 6.w,
                          height: 6.w,
                          child: CircularProgressIndicator(
                            color: AppTheme.lightTheme.colorScheme.surface,
                            strokeWidth: 2.0,
                          ),
                        )
                      : CustomIconWidget(
                          iconName: 'camera_alt',
                          color: AppTheme.lightTheme.colorScheme.surface,
                          size: 32,
                        ),
                ),
              ),
            ),

            // Flash toggle button
            GestureDetector(
              onTap: isProcessing ? null : onToggleFlash,
              child: Container(
                width: 14.w,
                height: 14.w,
                decoration: BoxDecoration(
                  color: currentFlashMode == FlashMode.off
                      ? AppTheme.lightTheme.colorScheme.surface
                          .withValues(alpha: 0.2)
                      : AppTheme.lightTheme.colorScheme.primary
                          .withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.lightTheme.colorScheme.surface
                        .withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                ),
                child: Center(
                  child: CustomIconWidget(
                    iconName: _getFlashIcon(),
                    color: isProcessing
                        ? AppTheme.lightTheme.colorScheme.surface
                            .withValues(alpha: 0.5)
                        : AppTheme.lightTheme.colorScheme.surface,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getFlashIcon() {
    switch (currentFlashMode) {
      case FlashMode.off:
        return 'flash_off';
      case FlashMode.auto:
        return 'flash_auto';
      case FlashMode.always:
        return 'flash_on';
      case FlashMode.torch:
        return 'flashlight_on';
      default:
        return 'flash_auto';
    }
  }
}
