import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/app_bottom_navigation_widget.dart';
import './widgets/care_reminders_widget.dart';
import './widgets/greeting_card_widget.dart';
import './widgets/my_plants_preview_widget.dart';
import './widgets/recent_scans_widget.dart';

class HomeDashboard extends StatefulWidget {
  const HomeDashboard({Key? key}) : super(key: key);

  @override
  State<HomeDashboard> createState() => _HomeDashboardState();
}

class _HomeDashboardState extends State<HomeDashboard>
    with TickerProviderStateMixin {
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  bool _isRefreshing = false;
  int _currentIndex = 0; // Dashboard tab index

  // Recent scans will be loaded from actual user data
  final List<Map<String, dynamic>> recentScans = [];

  final List<Map<String, dynamic>> myPlants = [];

  final List<Map<String, dynamic>> careReminders = [];

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _fabScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    super.dispose();
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isRefreshing = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Dashboard updated successfully'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _onAddToCollection(Map<String, dynamic> scan) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${scan['plantName']} added to your collection'),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'View',
          onPressed: () {
            Navigator.pushNamed(context, '/my-plant-collection');
          },
        ),
      ),
    );
  }

  void _onPlantTap(Map<String, dynamic> plant) {
    Navigator.pushNamed(context, '/my-plant-collection');
  }

  void _onCompleteTask(Map<String, dynamic> reminder) {
    // Handle task completion logic here
    // This could update the plant's last watered date, etc.
  }

  void _onFabPressed() {
    _fabAnimationController.forward().then((_) {
      _fabAnimationController.reverse();
    });
    Navigator.pushNamed(context, '/camera-capture');
  }

  int get _plantsNeedingAttention {
    return myPlants
        .where((plant) => plant['status'] == 'needs_attention')
        .length;
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Navigate to different screens based on tab
    switch (index) {
      case 0:
        // Current screen - Dashboard
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/camera-capture');
        break;
      case 2:
        Navigator.pushReplacementNamed(context, '/plant-health-diagnosis');
        break;
      case 3:
        Navigator.pushReplacementNamed(context, '/my-plant-collection');
        break;
      case 4:
        Navigator.pushReplacementNamed(context, '/profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _handleRefresh,
          color: AppTheme.lightTheme.colorScheme.primary,
          child: CustomScrollView(
            slivers: [
              SliverAppBar(
                expandedHeight: 0,
                floating: true,
                pinned: false,
                backgroundColor: Colors.transparent,
                elevation: 0,
                flexibleSpace: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppTheme.lightTheme.scaffoldBackgroundColor,
                        AppTheme.lightTheme.scaffoldBackgroundColor
                            .withValues(alpha: 0.0),
                      ],
                    ),
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 2.h),
                    GreetingCardWidget(
                      userName: "Sarah",
                      plantsNeedingAttention: _plantsNeedingAttention,
                    ),
                    SizedBox(height: 3.h),
                    RecentScansWidget(
                      recentScans: recentScans,
                      onAddToCollection: _onAddToCollection,
                    ),
                    SizedBox(height: 3.h),
                    MyPlantsPreviewWidget(
                      plants: myPlants,
                      onPlantTap: _onPlantTap,
                    ),
                    SizedBox(height: 3.h),
                    CareRemindersWidget(
                      reminders: careReminders,
                      onCompleteTask: _onCompleteTask,
                    ),
                    SizedBox(height: 10.h), // Bottom padding for FAB
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabScaleAnimation,
        child: FloatingActionButton.extended(
          onPressed: _onFabPressed,
          backgroundColor: AppTheme.lightTheme.colorScheme.secondary,
          foregroundColor: AppTheme.lightTheme.colorScheme.onSecondary,
          elevation: 6.0,
          icon: CustomIconWidget(
            iconName: 'camera_alt',
            color: AppTheme.lightTheme.colorScheme.onSecondary,
            size: 6.w,
          ),
          label: Text(
            'Scan Plant',
            style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      bottomNavigationBar: AppBottomNavigationWidget(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
      ),
    );
  }
}
