import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/plant_health_diagnosis_service.dart';
import '../../widgets/app_bottom_navigation_widget.dart';
import './widgets/custom_symptom_input.dart';
import './widgets/diagnosis_results.dart';
import './widgets/loading_analysis.dart';
import './widgets/photo_upload_section.dart';
import './widgets/symptom_checklist.dart';

class PlantHealthDiagnosis extends StatefulWidget {
  const PlantHealthDiagnosis({Key? key}) : super(key: key);

  @override
  State<PlantHealthDiagnosis> createState() => _PlantHealthDiagnosisState();
}

class _PlantHealthDiagnosisState extends State<PlantHealthDiagnosis> {
  int _currentIndex = 2; // Diagnosis tab index
  XFile? _selectedImage;
  List<String> _selectedSymptoms = [];
  String _customSymptom = '';
  bool _isAnalyzing = false;
  Map<String, dynamic>? _diagnosisResults;

  // Service for plant health diagnosis
  final PlantHealthDiagnosisService _diagnosisService = PlantHealthDiagnosisService();

  void _onImageSelected(XFile? image) {
    setState(() {
      _selectedImage = image;
    });
  }

  void _onSymptomsChanged(List<String> symptoms) {
    setState(() {
      _selectedSymptoms = symptoms;
    });
  }

  void _onCustomSymptomChanged(String symptom) {
    setState(() {
      _customSymptom = symptom;
    });
  }

  Future<void> _startAnalysis() async {
    if (_selectedImage == null &&
        _selectedSymptoms.isEmpty &&
        _customSymptom.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please upload a photo or select symptoms to analyze'),
          backgroundColor: AppTheme.lightTheme.colorScheme.error,
        ),
      );
      return;
    }

    setState(() {
      _isAnalyzing = true;
      _diagnosisResults = null;
    });

    try {
      debugPrint('Starting plant health diagnosis...');
      debugPrint('Selected symptoms: $_selectedSymptoms');
      debugPrint('Custom symptom: $_customSymptom');
      debugPrint('Selected image: ${_selectedImage?.path}');

      // Call the real plant health diagnosis API
      final Map<String, dynamic> diagnosisData = await _diagnosisService.diagnosePlantHealth(
        imagePath: _selectedImage?.path,
        symptoms: _selectedSymptoms.isNotEmpty ? _selectedSymptoms : null,
        customSymptom: _customSymptom.isNotEmpty ? _customSymptom : null,
      );

      debugPrint('Plant health diagnosis completed: ${diagnosisData['problem']}');

      if (mounted) {
        setState(() {
          _isAnalyzing = false;
          _diagnosisResults = diagnosisData;
        });
      }
    } catch (e) {
      debugPrint('Plant health diagnosis error: $e');

      if (mounted) {
        setState(() {
          _isAnalyzing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to analyze plant health. Please try again.'),
            backgroundColor: AppTheme.lightTheme.colorScheme.error,
          ),
        );
      }
    }
  }

  void _resetDiagnosis() {
    setState(() {
      _selectedImage = null;
      _selectedSymptoms.clear();
      _customSymptom = '';
      _diagnosisResults = null;
      _isAnalyzing = false;
    });
  }

  void _saveToProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Diagnosis saved to your plant profile'),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
      ),
    );
  }

  void _shareResults() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing diagnosis results...'),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
      ),
    );
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Navigate to different screens based on tab
    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/home-dashboard');
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/camera-capture');
        break;
      case 2:
        // Current screen - Diagnosis
        break;
      case 3:
        Navigator.pushReplacementNamed(context, '/my-plant-collection');
        break;
      case 4:
        Navigator.pushReplacementNamed(context, '/profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Plant Health Diagnosis',
          style: AppTheme.lightTheme.appBarTheme.titleTextStyle,
        ),
        backgroundColor: AppTheme.lightTheme.appBarTheme.backgroundColor,
        elevation: AppTheme.lightTheme.appBarTheme.elevation,
        actions: [
          if (_diagnosisResults != null)
            IconButton(
              onPressed: _resetDiagnosis,
              icon: CustomIconWidget(
                iconName: 'refresh',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              tooltip: 'New Diagnosis',
            ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.lightTheme.colorScheme.primary
                            .withValues(alpha: 0.1),
                        AppTheme.lightTheme.colorScheme.secondary
                            .withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      CustomIconWidget(
                        iconName: 'medical_services',
                        color: AppTheme.lightTheme.colorScheme.primary,
                        size: 48,
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        'AI Plant Doctor',
                        style: AppTheme.lightTheme.textTheme.headlineSmall
                            ?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        'Upload a photo and describe symptoms to get instant diagnosis and treatment recommendations',
                        style:
                            AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          color:
                              AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 3.h),
              ],

              // Photo Upload Section
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                PhotoUploadSection(
                  onImageSelected: _onImageSelected,
                ),
                SizedBox(height: 3.h),
              ],

              // Symptom Checklist
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                SymptomChecklist(
                  onSymptomsChanged: _onSymptomsChanged,
                ),
                SizedBox(height: 3.h),
              ],

              // Custom Symptom Input
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                CustomSymptomInput(
                  onCustomSymptomChanged: _onCustomSymptomChanged,
                ),
                SizedBox(height: 4.h),
              ],

              // Analyze Button
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _startAnalysis,
                    icon: CustomIconWidget(
                      iconName: 'psychology',
                      color: AppTheme.lightTheme.colorScheme.onPrimary,
                      size: 20,
                    ),
                    label: Text('Analyze Plant Health'),
                    style:
                        AppTheme.lightTheme.elevatedButtonTheme.style?.copyWith(
                      padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                      ),
                    ),
                  ),
                ),
              ],

              // Loading Analysis
              if (_isAnalyzing) ...[
                LoadingAnalysis(),
              ],

              // Diagnosis Results
              if (_diagnosisResults != null) ...[
                DiagnosisResults(
                  diagnosisData: _diagnosisResults!,
                  onSaveToProfile: _saveToProfile,
                  onShareResults: _shareResults,
                ),
              ],
            ],
          ),
        ),
      ),
      bottomNavigationBar: AppBottomNavigationWidget(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
      ),
    );
  }
}
