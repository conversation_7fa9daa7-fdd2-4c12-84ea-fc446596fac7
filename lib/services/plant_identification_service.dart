import 'package:flutter/foundation.dart';
import 'openrouter_service.dart';

class PlantIdentificationService {
  static final PlantIdentificationService _instance = PlantIdentificationService._internal();
  factory PlantIdentificationService() => _instance;
  PlantIdentificationService._internal();
  
  final OpenRouterService _openRouterService = OpenRouterService();
  
  /// Identifies a plant from an image path
  /// Returns structured plant data compatible with the existing UI
  Future<Map<String, dynamic>> identifyPlantFromImage(String imagePath) async {
    try {
      debugPrint('PlantIdentificationService: Starting identification for $imagePath');
      
      // Call OpenRouter service to identify the plant
      final Map<String, dynamic> plantData = await _openRouterService.identifyPlant(imagePath);
      
      // Add the image path to the plant data
      plantData['imagePath'] = imagePath;
      
      // Ensure compatibility with existing UI by adding required fields
      plantData['lightLevel'] = _calculateLightLevel(plantData['lightRequirement'] ?? '');
      plantData['waterLevel'] = _calculateWaterLevel(plantData['waterFrequency'] ?? '');
      plantData['temperatureLevel'] = _calculateTemperatureLevel(plantData['temperature'] ?? '');
      plantData['wateringFrequency'] = plantData['waterFrequency']; // Alias for compatibility
      plantData['temperatureRange'] = plantData['temperature']; // Alias for compatibility
      
      debugPrint('PlantIdentificationService: Identification completed successfully');
      debugPrint('Plant identified: ${plantData['commonName']} (${plantData['confidence']} confidence)');
      
      return plantData;
      
    } catch (e) {
      debugPrint('PlantIdentificationService: Error during identification: $e');
      
      // Return error data that's compatible with the UI
      return {
        'id': DateTime.now().millisecondsSinceEpoch,
        'commonName': 'Identification Error',
        'scientificName': 'Unable to process',
        'family': 'Unknown',
        'confidence': 0.0,
        'careLevel': 'Unknown',
        'lightRequirement': 'Unable to determine',
        'waterFrequency': 'Unable to determine',
        'wateringFrequency': 'Unable to determine',
        'temperature': 'Unable to determine',
        'temperatureRange': 'Unable to determine',
        'humidity': 'Unable to determine',
        'description': 'An error occurred while identifying this plant. Please check your internet connection and try again.',
        'growingTips': [
          'Ensure you have a stable internet connection',
          'Try taking a clearer photo',
          'Make sure the plant is well-lit in the image',
          'Contact support if the problem persists'
        ],
        'imagePath': imagePath,
        'lightLevel': 0.5,
        'waterLevel': 0.5,
        'temperatureLevel': 0.5,
        'error': e.toString(),
      };
    }
  }
  
  /// Calculates a normalized light level (0.0 to 1.0) based on light requirement text
  double _calculateLightLevel(String lightRequirement) {
    final String lower = lightRequirement.toLowerCase();
    
    if (lower.contains('low') || lower.contains('shade')) {
      return 0.3;
    } else if (lower.contains('medium') || lower.contains('indirect')) {
      return 0.6;
    } else if (lower.contains('bright') || lower.contains('direct') || lower.contains('full sun')) {
      return 0.9;
    }
    
    return 0.6; // Default to medium light
  }
  
  /// Calculates a normalized water level (0.0 to 1.0) based on watering frequency
  double _calculateWaterLevel(String waterFrequency) {
    final String lower = waterFrequency.toLowerCase();
    
    if (lower.contains('daily') || lower.contains('every day')) {
      return 0.9;
    } else if (lower.contains('every other day') || lower.contains('2-3 times')) {
      return 0.8;
    } else if (lower.contains('weekly') || lower.contains('once a week')) {
      return 0.6;
    } else if (lower.contains('bi-weekly') || lower.contains('every two weeks')) {
      return 0.4;
    } else if (lower.contains('monthly') || lower.contains('once a month')) {
      return 0.2;
    }
    
    return 0.5; // Default to moderate watering
  }
  
  /// Calculates a normalized temperature level (0.0 to 1.0) based on temperature range
  double _calculateTemperatureLevel(String temperatureRange) {
    final String lower = temperatureRange.toLowerCase();
    
    // Extract temperature numbers if possible
    final RegExp tempRegex = RegExp(r'(\d+)[-–](\d+)');
    final Match? match = tempRegex.firstMatch(lower);
    
    if (match != null) {
      try {
        final int minTemp = int.parse(match.group(1)!);
        final int maxTemp = int.parse(match.group(2)!);
        final double avgTemp = (minTemp + maxTemp) / 2;
        
        // Normalize temperature (assuming 60-80°F is the normal range)
        if (avgTemp < 60) {
          return 0.3; // Cool
        } else if (avgTemp > 80) {
          return 0.9; // Warm
        } else {
          return 0.6; // Normal room temperature
        }
      } catch (e) {
        debugPrint('Error parsing temperature: $e');
      }
    }
    
    // Fallback based on keywords
    if (lower.contains('cool') || lower.contains('cold')) {
      return 0.3;
    } else if (lower.contains('warm') || lower.contains('hot')) {
      return 0.8;
    }
    
    return 0.6; // Default to room temperature
  }
  
  /// Validates if an image file exists and is accessible
  Future<bool> validateImagePath(String imagePath) async {
    try {
      // This is a basic validation - in a real app you might want more thorough checks
      return imagePath.isNotEmpty && 
             (imagePath.endsWith('.jpg') || 
              imagePath.endsWith('.jpeg') || 
              imagePath.endsWith('.png') || 
              imagePath.endsWith('.webp'));
    } catch (e) {
      debugPrint('Error validating image path: $e');
      return false;
    }
  }
  

}
