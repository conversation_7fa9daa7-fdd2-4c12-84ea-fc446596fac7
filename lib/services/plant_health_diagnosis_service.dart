import 'package:flutter/foundation.dart';
import 'openrouter_service.dart';

class PlantHealthDiagnosisService {
  final OpenRouterService _openRouterService = OpenRouterService();
  
  /// Diagnoses plant health issues from image and/or symptoms
  /// Returns structured diagnosis data compatible with the existing UI
  Future<Map<String, dynamic>> diagnosePlantHealth({
    String? imagePath,
    List<String>? symptoms,
    String? customSymptom,
  }) async {
    try {
      debugPrint('PlantHealthDiagnosisService: Starting diagnosis...');
      
      // Validate inputs
      if ((symptoms == null || symptoms.isEmpty) && 
          (customSymptom == null || customSymptom.isEmpty) && 
          (imagePath == null || imagePath.isEmpty)) {
        throw Exception('No symptoms or image provided for analysis');
      }
      
      // Call OpenRouter service to diagnose plant health
      final Map<String, dynamic> diagnosisData = await _openRouterService.diagnosePlantHealth(
        imagePath: imagePath,
        symptoms: symptoms,
        customSymptom: customSymptom,
      );
      
      debugPrint('PlantHealthDiagnosisService: Diagnosis completed successfully');
      debugPrint('Problem identified: ${diagnosisData['problem']} (${diagnosisData['confidence']}% confidence)');
      
      return diagnosisData;
      
    } catch (e) {
      debugPrint('PlantHealthDiagnosisService: Error during diagnosis: $e');
      
      // Return fallback diagnosis data
      return _createFallbackDiagnosis(e.toString());
    }
  }
  
  /// Creates fallback diagnosis data when the service fails
  Map<String, dynamic> _createFallbackDiagnosis(String error) {
    return {
      'confidence': 0.0,
      'severity': 'Unknown',
      'problem': 'Diagnosis Service Unavailable',
      'treatments': [
        'Check your internet connection and try again',
        'Ensure you have provided symptoms or uploaded an image',
        'Try again in a few moments',
        'Consider consulting a local plant expert',
      ],
      'timeline': 'Unable to determine - service unavailable',
      'followUp': [
        'Retry diagnosis when connection is restored',
        'Document symptoms for future reference',
        'Seek alternative plant care resources',
      ],
      'error': error,
    };
  }
}
